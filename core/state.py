"""
State definitions for the LangGraph agent.

This module defines a simplified state schema for the basic START -> llm -> END flow.
"""

from typing import List, Optional, Annotated

from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict

from logger.get_logger import log

# TODO: impelemnt these changes: # class AgentState(TypedDict):
#     messages: Annotated[list[BaseMessage], add_messages]
#     summary: str
#     window_size: int  # e.g., 5 messages before triggering summarization
#     user_input: str  # optional—latest user input
#     final_answer: str  # optional—LLM answer
#     # Internal cached instances below (underscore prefix)
#     _llm: object   # cached LLM instance (not passed in graph state as user input)
class AgentState(TypedDict):
    """
    Simplified state schema for the LangGraph agent.

    This state maintains basic conversation context for the simple flow.
    """
    # Message history with automatic message aggregation
    messages: Annotated[List[BaseMessage], add_messages]

    # Current user input being processed
    user_input: Optional[str]

    # Final answer from LLM
    final_answer: Optional[str]

    # Error handling
    error: Optional[str]


@log
def create_initial_state(user_input: str) -> AgentState:
    """
    Create an initial state for a new agent conversation.

    Args:
        user_input: The user's initial input/question

    Returns:
        Initial AgentState with user input and default values
    """
    return AgentState(
            messages = [HumanMessage(content = user_input)],
            user_input = user_input,
            final_answer = None,
            error = None
    )
