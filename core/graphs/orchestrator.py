"""
Agent orchestrator for managing LangGraph execution.

This module provides a high-level interface for running the simplified agent graph.
"""

import uuid
from typing import Dict, Any, Optional

from core.tools import ToolRegistry
from logger.get_logger import log
from .agent_graph import create_agent_graph
from ..state import create_initial_state


class AgentOrchestrator:
    """
    High-level orchestrator for the simplified LangGraph agent.

    This class provides a clean interface for running simple conversations.

    Note: The enhanced flow includes summarization for better context management.
    The graph structure is: START → summarizer → llm_reasoning_node → END.
    The steps_taken field reflects the actual nodes executed in the flow.
    """

    def __init__(self):
        """
        Initialize the agent orchestrator.
        """
        self.graph = create_agent_graph()
        self.tool_registry = ToolRegistry()  # Keep for future use

    @log
    def run_conversation(
            self,
            user_input: str,
            thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Run a simple conversation with the agent.

        Args:
            user_input: The user's input/question
            thread_id: Optional thread ID for conversation persistence

        Returns:
            Dictionary with final answer and metadata
        """
        # Generate thread ID if not provided
        if not thread_id:
            thread_id = str(uuid.uuid4())

        # Create initial state
        initial_state = create_initial_state(user_input = user_input)

        # Configure graph execution
        config = {
                "configurable": {
                        "thread_id": thread_id
                }
        }

        try:
            # Run the graph
            final_state = None
            for state in self.graph.stream(initial_state, config):
                final_state = state

            # Extract results from the final state
            result = self._extract_conversation_result(final_state, thread_id)
            return result

        except Exception as e:
            return {
                    "success"        : False,
                    "error"          : str(e),
                    "final_answer"   : f"I encountered an error: {str(e)}",
                    "reasoning_steps": [],
                    "steps_taken"    : 1,
                    "thread_id"      : thread_id
            }

    def _extract_conversation_result(self, final_state: Dict[str, Any], thread_id: str) -> Dict[str, Any]:
        """
        Extract the final conversation result from the graph state.

        Args:
            final_state: Final state from graph execution
            thread_id: Thread ID for the conversation

        Returns:
            Formatted conversation result
        """
        # Get the actual state (LangGraph returns nested structure)
        if isinstance(final_state, dict) and len(final_state) == 1:
            state_key = list(final_state.keys())[0]
            state = final_state[state_key]
        else:
            state = final_state

        return {
                "success"        : not bool(state.get("error")),
                "final_answer"   : state.get("final_answer", "I couldn't provide an answer."),
                "reasoning_steps": [],  # Enhanced flow with summarization
                "steps_taken"    : 2,  # Two steps: summarizer + llm_reasoning_node
                "tool_results"   : {},  # No tools in basic flow
                "error"          : state.get("error"),
                "thread_id"      : thread_id
        }
