"""
Agent graph definition using LangGraph.

This module creates a simple agent graph: START -> llm -> END
"""

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, START, END

from ..nodes.llm_node import llm_reasoning_node
from ..nodes.summarization_node import summarization_node
from ..state import Agent<PERSON>tate
def create_agent_graph():
    """
    Create the main agent graph using LangGraph.

    Simple graph structure:
    START -> LLM -> END

    Returns:
        Compiled LangGraph with memory checkpointing
    """
    # Create the state graph
    graph = StateGraph(AgentState)

    # Add single LLM node
    graph.add_node("llm", llm_reasoning_node)

    # Add simple edges: START -> llm -> END
    graph.add_edge(START, "llm")
    graph.add_edge("llm", END)

    # Add memory for conversation persistence
    # MemorySaver provides in-memory checkpointing for conversation state
    # This allows the graph to maintain conversation history across interactions
    # within the same thread_id, enabling context-aware responses
    memory = MemorySaver()

    # Compile the graph
    graph = graph.compile(checkpointer = memory)

    return graph


def visualize_graph(graph):
    """
    Generate a visual representation of the graph and log it.

    This function creates a Mermaid diagram of the graph structure and logs it
    for debugging purposes. Called once per main.py execution to show the
    current graph topology.

    Args:
        graph: Compiled LangGraph

    Returns:
        Graph visualization (if available)
    """
    try:
        # Generate Mermaid diagram for the graph structure
        mermaid_diagram = graph.get_graph().draw_mermaid()

        # Log the graph structure for debugging
        print("📊 Current Graph Structure:")
        print("=" * 50)
        print("Simple LangGraph Flow: START → llm_reasoning_node → END")
        print("=" * 50)

        return mermaid_diagram
    except Exception as e:
        print(f"⚠️ Could not generate graph visualization: {e}")
        return None
