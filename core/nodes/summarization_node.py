from langchain_core.messages import SystemMessage, HumanMessage

from core.state import Agent<PERSON><PERSON>
from logger.get_logger import get_logger, LOG_LEVEL
import logging

logger = get_logger("summarizer")
logger.setLevel(LOG_LEVEL)

# TODO: fix logging to use @log decorator and logger.get_logger() - so it writes to dabot-agent.log
def summarization_node(state: AgentState) -> AgentState:
    """
    Summarizes conversation dynamically if messages exceed window_size,
    updates summary, and prunes old messages.
    Uses actual LLM call to produce concise summary and caches the LLM instance.
    """
    if len(state.get("messages", [])) > state.get("window_size", 5):
        logger.debug(f"Summarization triggered: {len(state['messages'])} messages, window size {state['window_size']}")

        # Get cached LLM instance
        llm = get_cached_llm(state)

        # Prepare concatenated recent messages as input to summarizer LLM
        recent_msgs = state.get("messages", [])
        messages_text = "\n".join(msg.content for msg in recent_msgs if hasattr(msg, "content"))

        # Compose summarization prompt
        prompt = (
                "You are a helpful assistant. Summarize the following conversation concisely, "
                "highlighting key facts and important information the assistant should remember:\n\n"
                f"{messages_text}\n\n"
                "Provide the summary as a brief text."
        )

        summarization_msgs = [
                SystemMessage(content = "You are a conversation summarizer."),
                HumanMessage(content = prompt)
        ]

        logger.debug(f"Sending summarization request to LLM with {len(messages_text)} characters of conversation.")

        response = llm.invoke(summarization_msgs)
        new_summary = response.content.strip()

        logger.debug(f"LLM-generated summary:\n{new_summary}")

        # Update the summary in state
        state["summary"] = new_summary

        # Prune messages to keep only the last 'window_size'
        state["messages"] = state["messages"][-state["window_size"]:]

        logger.debug(f"Pruned messages to last {state['window_size']} entries.")
    else:
        logger.debug(f"Summarization not triggered: {len(state.get('messages', []))} messages, below threshold.")

    return state
