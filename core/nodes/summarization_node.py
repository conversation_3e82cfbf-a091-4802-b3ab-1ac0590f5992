from langchain_core.messages import SystemMessage, HumanMessage

from core.state import AgentState
from logger.get_logger import log
from .llm_node import get_cached_llm


@log
def summarization_node(state: AgentState) -> AgentState:
    if len(state.get("messages", [])) > state.get("window_size", 5):
        logger.debug(f"Summarization triggered with {len(state['messages'])} messages.")

        # TODO: implement get_cached_llm() after refactoring llm_node.py
        llm = get_cached_llm(state)

        messages_text = "\n".join(msg.content for msg in state.get("messages", []) if hasattr(msg, "content"))

        prompt = (
                "You are a helpful assistant. Summarize the following conversation concisely, "
                "highlighting key facts and important information the assistant should remember:\n\n"
                f"{messages_text}\n\n"
                "Provide the summary as a brief text."
        )

        summarization_msgs = [
                SystemMessage(content = "You are a conversation summarizer."),
                HumanMessage(content = prompt)
        ]

        logger.debug(f"Sending summarization request to LLM.")

        response = llm.invoke(summarization_msgs)
        new_summary = response.content.strip()

        logger.debug(f"LLM-generated summary: {new_summary}")

        state["summary"] = new_summary
        state["messages"] = state["messages"][-state["window_size"]:]
        logger.debug(f"Pruned messages to last {state['window_size']} entries.")
    else:
        logger.debug(f"Summarization not triggered; message count below threshold.")

    return state
