from langchain_core.messages import SystemMessage, HumanMessage

from core.state import Agent<PERSON><PERSON>
from logger.get_logger import log, get_logger
from .llm_node import get_cached_llm


@log
def summarization_node(state: AgentState) -> AgentState:
    """
    Summarizes conversation dynamically if messages exceed window_size,
    updates summary, and prunes old messages.
    Uses actual LLM call to produce concise summary and caches the LLM instance.

    Args:
        state: Current agent state

    Returns:
        Updated state with summary and pruned messages
    """
    if len(state.get("messages", [])) > state.get("window_size", 5):
        # Get cached LLM instance
        llm = get_cached_llm(state)

        # Prepare conversation text for summarization
        messages_text = "\n".join([
            f"{msg.type}: {msg.content}"
            for msg in state.get("messages", [])
            if hasattr(msg, "content") and hasattr(msg, "type")
        ])

        prompt = (
                "You are a helpful assistant. Summarize the following conversation concisely, "
                "highlighting key facts and important information the assistant should remember:\n\n"
                f"{messages_text}\n\n"
                "Provide the summary as a brief text."
        )

        summarization_msgs = [
                SystemMessage(content = "You are a conversation summarizer."),
                HumanMessage(content = prompt)
        ]

        logger.debug(f"Sending summarization request to LLM.")

        response = llm.invoke(summarization_msgs)
        new_summary = response.content.strip()

        logger.debug(f"LLM-generated summary: {new_summary}")

        state["summary"] = new_summary
        state["messages"] = state["messages"][-state["window_size"]:]
        logger.debug(f"Pruned messages to last {state['window_size']} entries.")
    else:
        logger.debug(f"Summarization not triggered; message count below threshold.")

    return state
