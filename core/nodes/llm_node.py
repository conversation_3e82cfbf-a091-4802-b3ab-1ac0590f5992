"""
LLM reasoning node for the LangGraph agent.

This node handles simple LLM interaction following LangGraph best practices.
"""

from typing import Dict, Any
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from models.llamacpp_provider import LlamaCppProvider
from config import get_model_config, LLAMACPP_API_URL
from logger.get_logger import log
from logger.get_logger import get_logger
from config import LOG_LEVEL
from ..state import AgentState


# TODO: implement this:
#  def get_cached_llm(state: dict):
#     """
#     Returns a cached LLM instance stored in the agent state.
#     Initializes it if missing.
#     """
#     if "_llm" not in state or state["_llm"] is None:
#         provider = LlamaCppProvider(config=get_model_config(), api_url=LLAMACPP_API_URL)
#         state["_llm"] = provider.get_llm()
#     return state["_llm"]
@log
def llm_reasoning_node(state: AgentState) -> Dict[str, Any]:
    """
    LLM reasoning node following LangGraph best practices with proper message roles.

    Args:
        state: Current agent state

    Returns:
        Updated state with LLM response and final_answer
    """
    # Create logger with specific name for this node
    import logging
    logger = logging.getLogger("llm_reasoning_node")
    logger.setLevel(LOG_LEVEL)

    # Get user input from the latest human message or initial state
    logger.debug(f"🧠 LLM reasoning node started")
    logger.debug(f"📊 Input state keys: {list(state.keys())}")
    logger.debug(f"📝 Messages count: {len(state.get('messages', []))}")

    user_input = state.get("user_input", "")
    if not user_input and state.get("messages"):
        # Extract from the first human message
        for msg in state["messages"]:
            if hasattr(msg, 'type') and msg.type == 'human':
                user_input = msg.content
                break

    logger.debug(f"💬 Extracted user input: '{user_input}'")

    # Get LLM provider
    logger.debug("🔧 Initializing LLM provider")
    provider = LlamaCppProvider(
            config = get_model_config(),
            api_url = LLAMACPP_API_URL
    )
    llm = provider.get_llm()

    # Create proper message structure following LangGraph best practices
    messages = [
            SystemMessage(
                    content = """You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user questions."""),
            HumanMessage(content = user_input)
    ]

    # Call LLM with proper message structure
    logger.debug(f"📤 Sending {len(messages)} messages to LLM")
    logger.debug(f"📋 System message: {messages[0].content[:100]}...")
    logger.debug(f"👤 Human message: {messages[1].content}")

    response = llm.invoke(messages)
    logger.debug(f"📥 LLM response received: {response.content[:100]}...")

    # Update state with LLM response
    updated_state = {
            **state,
            "final_answer": response.content
    }

    # Add LLM response to messages following LangGraph patterns
    if "messages" not in updated_state:
        updated_state["messages"] = []
    updated_state["messages"].append(AIMessage(content = response.content))

    logger.debug(f"updated_state: {updated_state}")
    return updated_state
