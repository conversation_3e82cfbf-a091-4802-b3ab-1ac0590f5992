"""
LLM reasoning node for the LangGraph agent.

This node handles simple LLM interaction following LangGraph best practices.
"""

from typing import Dict, Any
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from models.llamacpp_provider import LlamaCppProvider
from config import get_model_config, LLAMACPP_API_URL
from logger.get_logger import log
from logger.get_logger import get_logger
from config import LOG_LEVEL
from ..state import AgentState

# Module-level cache for LLM instance to avoid serialization issues
_cached_llm = None


@log
def get_cached_llm(state: dict):
    """
    Returns a cached LLM instance stored in the agent state.
    Initializes it if missing.

    Args:
        state: Agent state dictionary

    Returns:
        Cached LLM instance
    """
    if "_llm" not in state or state["_llm"] is None:
        provider = LlamaCppProvider(config=get_model_config(), api_url=LLAMACPP_API_URL)
        state["_llm"] = provider.get_llm()
    return state["_llm"]
@log
def llm_reasoning_node(state: AgentState) -> Dict[str, Any]:
    """
    LLM reasoning node following LangGraph best practices with proper message roles.
    Uses cached LLM instance and includes conversation summary.

    Args:
        state: Current agent state

    Returns:
        Updated state with LLM response and final_answer
    """
    # Get user input from the latest human message or initial state
    user_input = state.get("user_input", "")
    if not user_input and state.get("messages"):
        # Extract from the most recent human message
        for msg in reversed(state["messages"]):
            if hasattr(msg, 'type') and msg.type == 'human':
                user_input = msg.content
                break

    # Get cached LLM instance for performance
    llm = get_cached_llm(state)

    # Include conversation summary in system prompt if available
    summary = state.get("summary", "")
    system_content = "You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user questions."
    if summary:
        system_content += f"\n\nConversation summary:\n{summary}"

    # Create proper message structure following LangGraph best practices
    messages = [
        SystemMessage(content=system_content),
        HumanMessage(content=user_input)
    ]

    # Call LLM with proper message structure
    response = llm.invoke(messages)

    # Update state with LLM response
    updated_state = {
        **state,
        "final_answer": response.content
    }

    # Add LLM response to messages following LangGraph patterns
    if "messages" not in updated_state:
        updated_state["messages"] = []
    updated_state["messages"].append(AIMessage(content=response.content))

    return updated_state

