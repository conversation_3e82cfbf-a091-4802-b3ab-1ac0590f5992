"""
LLM reasoning node for the LangGraph agent.

This node handles simple LLM interaction following LangGraph best practices.
"""

from typing import Dict, Any
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from models.llamacpp_provider import LlamaCppProvider
from config import get_model_config, LLAMACPP_API_URL
from logger.get_logger import log
from logger.get_logger import get_logger
from config import LOG_LEVEL
from ..state import AgentState

# Module-level cache for LLM instance to avoid serialization issues
_cached_llm = None


@log
def get_cached_llm(state: dict = None):
    """
    Returns a cached LLM instance stored at module level.
    Initializes it if missing. Uses module-level caching to avoid
    serialization issues with LangGraph state.

    Args:
        state: Agent state dictionary (unused, kept for compatibility)

    Returns:
        Cached LLM instance
    """
    global _cached_llm
    if _cached_llm is None:
        provider = LlamaCppProvider(config=get_model_config(), api_url=LLAMACPP_API_URL)
        _cached_llm = provider.get_llm()
    return _cached_llm
@log
def llm_reasoning_node(state: AgentState) -> Dict[str, Any]:
    """
    LLM reasoning node following LangGraph best practices with proper message roles.
    Uses cached LLM instance and includes conversation history and summary.

    Args:
        state: Current agent state

    Returns:
        Updated state with LLM response and final_answer
    """
    # Get logger for detailed internal logging
    logger = get_logger()

    # Get user input from the latest human message or initial state
    user_input = state.get("user_input", "")
    if not user_input and state.get("messages"):
        # Extract from the most recent human message
        for msg in reversed(state["messages"]):
            if hasattr(msg, 'type') and msg.type == 'human':
                user_input = msg.content
                break

    logger.debug(f"llm_reasoning_node - Processing user input: '{user_input}'")
    logger.debug(f"llm_reasoning_node - Total messages in history: {len(state.get('messages', []))}")

    # Get cached LLM instance for performance
    llm = get_cached_llm(state)

    # Include conversation summary in system prompt if available
    summary = state.get("summary", "")
    system_content = "You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user questions."
    if summary:
        system_content += f"\n\nConversation summary:\n{summary}"
        logger.debug(f"llm_reasoning_node - Using conversation summary: {summary[:100]}...")

    # Use the FULL conversation history, not just the current input
    messages = [SystemMessage(content=system_content)]

    # Add all conversation messages to maintain context
    conversation_messages = state.get("messages", [])
    if conversation_messages:
        messages.extend(conversation_messages)
        logger.debug(f"llm_reasoning_node - Sending {len(messages)} messages to LLM (including system + {len(conversation_messages)} conversation messages)")
    else:
        # Fallback to just current input if no history
        messages.append(HumanMessage(content=user_input))
        logger.debug(f"llm_reasoning_node - No conversation history, using current input only")

    # Log the messages being sent (truncated for readability)
    for i, msg in enumerate(messages):
        msg_type = type(msg).__name__
        content_preview = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
        logger.debug(f"llm_reasoning_node - Message {i}: {msg_type}: {content_preview}")

    # Call LLM with full conversation context
    logger.debug(f"llm_reasoning_node - Invoking LLM with {len(messages)} messages")
    response = llm.invoke(messages)
    logger.debug(f"llm_reasoning_node - LLM response received: {response.content[:100]}...")

    # Update state with LLM response
    updated_state = {
        **state,
        "final_answer": response.content
    }

    # Add LLM response to messages following LangGraph patterns
    if "messages" not in updated_state:
        updated_state["messages"] = []
    updated_state["messages"].append(AIMessage(content=response.content))

    return updated_state

