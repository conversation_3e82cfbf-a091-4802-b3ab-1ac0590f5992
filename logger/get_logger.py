import os
import functools
import logging

# Import log level from centralized config
from config import LOG_LEVEL

# Create a logger
logger = logging.getLogger(__name__)
logger.setLevel(getattr(logging, LOG_LEVEL))

# Create a directory for log files
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
log_file = os.path.join(root_dir, "dabot-agent.log")

# Create a file handler
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.DEBUG)

# Create a console handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# Create a formatter and set it for the handlers
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)


# Define the logger decorator
def log(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            logger.debug(f"Calling {func.__name__} with arguments: {args}, {kwargs}")
            result = func(*args, **kwargs)
            logger.debug(f"{func.__name__} returned: {result}")
            return result
        except Exception as e:
            # Get the logger name from the module where the decorator is applied
            logger_name = func.__module__
            logger.error(f"{logger_name} - {func.__name__} function raised an exception: {e}")
            raise

    return wrapper


def get_logger():
    """
    Get the configured logger instance.

    This function returns the pre-configured logger that can be used
    for manual logging when the @log decorator is not suitable.

    Returns:
        logging.Logger: Configured logger instance

    Example:
        from logger.get_logger import get_logger

        logger = get_logger()
        logger.info("This is an info message")
        logger.debug("Debug information")
        logger.error("Error occurred")
    """
    return logger
